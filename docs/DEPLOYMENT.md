# LoopCraft 部署指南

本文档提供了 LoopCraft AI 音乐生成平台的完整部署指南。

## 快速部署

### 1. Vercel 部署（推荐）

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fopcraft%2Floopcraft&project-name=my-loopcraft-project&repository-name=my-loopcraft-project&redirect-url=https%3A%2F%2Floopcraft.app&demo-title=LoopCraft&demo-description=AI-powered%20loop%20background%20music%20generation%20platform&demo-url=https%3A%2F%2Floopcraft.app)

1. 点击上方按钮
2. 连接你的 GitHub 账户
3. 配置环境变量（见下方配置部分）
4. 部署完成

### 2. 手动部署到 Vercel

```bash
# 1. 克隆项目
git clone https://github.com/opcraft/loopcraft.git
cd loopcraft

# 2. 安装依赖
pnpm install

# 3. 配置环境变量
cp .env.example .env.local

# 4. 构建项目
pnpm build

# 5. 部署到 Vercel
npx vercel --prod
```

## 环境变量配置

### 必需配置

```bash
# 数据库配置
DATABASE_URL="postgresql://username:password@host:port/database"

# 认证配置
AUTH_SECRET="your-auth-secret"
AUTH_URL="https://your-domain.com/api/auth"

# AI 音乐生成服务
MUBERT_API_KEY="your-mubert-api-key"
SUNO_API_KEY="your-suno-api-key"

# 支付配置
STRIPE_PUBLIC_KEY="pk_live_..."
STRIPE_PRIVATE_KEY="sk_live_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# 文件存储配置
STORAGE_ENDPOINT="https://s3.amazonaws.com"
STORAGE_REGION="us-east-1"
STORAGE_ACCESS_KEY="your-access-key"
STORAGE_SECRET_KEY="your-secret-key"
STORAGE_BUCKET="your-bucket-name"
STORAGE_DOMAIN="https://your-cdn-domain.com"
```

### 可选配置

```bash
# Google 认证
AUTH_GOOGLE_ID="your-google-client-id"
AUTH_GOOGLE_SECRET="your-google-client-secret"

# GitHub 认证
AUTH_GITHUB_ID="your-github-client-id"
AUTH_GITHUB_SECRET="your-github-client-secret"

# 分析服务
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="GA_MEASUREMENT_ID"
NEXT_PUBLIC_PLAUSIBLE_DOMAIN="your-domain.com"

# 管理员邮箱
ADMIN_EMAILS="<EMAIL>,<EMAIL>"
```

## 数据库设置

### 1. PostgreSQL 数据库

推荐使用 Supabase 或 PlanetScale：

```bash
# Supabase
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"

# PlanetScale
DATABASE_URL="mysql://[username]:[password]@[host]/[database]?sslaccept=strict"
```

### 2. 数据库迁移

```bash
# 生成迁移文件
pnpm db:generate

# 执行迁移
pnpm db:migrate

# 推送 schema 到数据库
pnpm db:push
```

## AI 服务配置

### 1. Mubert API

1. 访问 [Mubert B2B](https://mubert.com/b2b)
2. 注册账户并获取 API 密钥
3. 设置环境变量：
   ```bash
   MUBERT_API_KEY="your-mubert-api-key"
   MUBERT_BASE_URL="https://api-b2b.mubert.com/v2"
   ```

### 2. Suno API

1. 访问 [Suno AI](https://suno.ai/)
2. 获取 API 访问权限
3. 设置环境变量：
   ```bash
   SUNO_API_KEY="your-suno-api-key"
   SUNO_BASE_URL="https://api.suno.ai/v1"
   ```

## 支付系统配置

### Stripe 配置

1. 创建 [Stripe](https://stripe.com/) 账户
2. 获取 API 密钥
3. 配置 Webhook 端点：`https://your-domain.com/api/stripe-notify`
4. 设置环境变量

## 文件存储配置

### AWS S3 配置

1. 创建 S3 存储桶
2. 配置 CORS 策略
3. 创建 IAM 用户并获取访问密钥
4. 设置环境变量

### CORS 策略示例

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedOrigins": ["https://your-domain.com"],
    "ExposeHeaders": []
  }
]
```

## 性能优化

### 1. CDN 配置

- 配置 CloudFront 或其他 CDN
- 缓存静态资源和音频文件
- 设置适当的缓存策略

### 2. 数据库优化

- 配置连接池
- 添加适当的索引
- 定期清理过期数据

### 3. 监控配置

- 设置错误监控（Sentry）
- 配置性能监控
- 设置日志聚合

## 安全配置

### 1. 环境变量安全

- 使用强密码和随机密钥
- 定期轮换 API 密钥
- 限制 API 密钥权限

### 2. 网络安全

- 启用 HTTPS
- 配置 CSP 头
- 设置适当的 CORS 策略

### 3. 数据库安全

- 使用 SSL 连接
- 限制数据库访问权限
- 定期备份数据

## 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本（推荐 18+）
   - 确保所有依赖已安装
   - 检查环境变量配置

2. **数据库连接失败**
   - 验证 DATABASE_URL 格式
   - 检查网络连接
   - 确认数据库权限

3. **API 调用失败**
   - 验证 API 密钥
   - 检查 API 配额
   - 查看错误日志

### 日志查看

```bash
# Vercel 日志
npx vercel logs

# 本地开发日志
pnpm dev
```

## 维护指南

### 1. 定期更新

- 更新依赖包
- 监控安全漏洞
- 备份数据库

### 2. 监控指标

- 响应时间
- 错误率
- 用户活跃度
- 资源使用情况

### 3. 扩展计划

- 数据库分片
- 微服务架构
- 负载均衡

## 支持

如果在部署过程中遇到问题，请：

1. 查看 [GitHub Issues](https://github.com/opcraft/loopcraft/issues)
2. 参考 [文档](https://docs.loopcraft.app)
3. 联系技术支持

---

部署成功后，你将拥有一个功能完整的 AI 音乐生成平台！
